import { useEffect, useMemo, useCallback, useState } from "react";
import { useRpcSetup } from "../queriesSetup/useRpcSetup";
import { useDashboardQueries } from "./useDashboardQueries";
import { getCommunityPoolByDenom } from "@/utils/getCommunityPool";
import { getTotalSupplyByDenom } from "@/utils/getTotalSupplyByDenom";
import { getExponent, toProperAmount } from "@/utils";
import {
  handleGetTestnetCirculatingSupply,
  handleGetMainnetCirculatingSupply,
  handleGetDevnetCirculatingSupply,
} from "@/api/getCirculatingSupply";

// Hook do pobierania danych bez aktualizowania store'a
export const useDashboardData = (chainName: string | undefined) => {
  const { empeQuery, cosmosQuery, isDataQueryEnabled } = useRpcSetup(chainName);
  const [circulatingSupply, setCirculatingSupply] = useState<number | null>(
    null
  );
  const [isLoadingCirculatingSupply, setIsLoadingCirculatingSupply] =
    useState(false);

  //QUERIES
  const { allQueries, queriesWithUnchangingKeys } = useDashboardQueries({
    empeQuery,
    cosmosQuery,
    isDataQueryEnabled,
  });

  //QUERIES LIFE CYCLE
  const isInitialFetching = Object.values(allQueries).some(
    ({ isLoading }) => isLoading
  );

  const isRefetching = Object.values(allQueries).some(
    ({ isRefetching }) => isRefetching
  );

  const isRpcLoading = isInitialFetching || isRefetching;

  // Całkowity stan ładowania (RPC + REST)
  const isLoading = isRpcLoading || isLoadingCirculatingSupply;

  type AllQueries = typeof allQueries;

  type QueriesData = {
    [Key in keyof AllQueries]: NonNullable<AllQueries[Key]["data"]>;
  };

  // Fetch circulating supply from REST endpoints
  const fetchCirculatingSupply = useCallback(async () => {
    if (!chainName) return null;

    setIsLoadingCirculatingSupply(true);

    try {
      // Get circulating supply based on chainName
      if (chainName.includes("testnet")) {
        const response = await handleGetTestnetCirculatingSupply();
        const supply = response;
        setCirculatingSupply(supply);
        return supply;
      } else if (chainName.includes("devnet")) {
        const response = await handleGetDevnetCirculatingSupply();
        const supply = response;
        setCirculatingSupply(supply);
        return supply;
      } else {
        const response = await handleGetMainnetCirculatingSupply();
        const supply = response;
        setCirculatingSupply(supply);
        return supply;
      }
    } catch (error) {
      console.error("Error fetching circulating supply:", error);
      return null;
    } finally {
      setIsLoadingCirculatingSupply(false);
    }
  }, [chainName]);

  //DATA
  const data = useMemo(() => {
    if (isRpcLoading) return undefined;

    const queriesData = Object.fromEntries(
      Object.entries(allQueries).map(([key, query]) => [key, query.data])
    ) as QueriesData;

    const {
      inflation,
      communityPool,
      totalSupply,
      didDocumentCount,
      bondedTokens,
      tokenBurned,
    } = queriesData;

    const inflationValue = inflation?.inflation;

    const communityPoolByDenom = getCommunityPoolByDenom(
      "uempe",
      communityPool?.pool || []
    ).amount;

    const totalSupplyByDenom = getTotalSupplyByDenom(
      "uempe",
      totalSupply?.supply || []
    ).amount;

    const didDocumentCountValue = didDocumentCount?.count;
    const bondedTokensValue = bondedTokens.pool.bondedTokens;
    const tokenBurnedValue = tokenBurned.balance.amount;

    const exp = getExponent(chainName);
    const totalSupplyAmount = toProperAmount(totalSupplyByDenom, exp);
    const bondedTokensAmount = toProperAmount(bondedTokensValue, exp);
    const communityPoolAmount = toProperAmount(communityPoolByDenom, exp);

    return {
      inflation: inflationValue,
      communityPool: communityPoolAmount,
      totalSupply: totalSupplyAmount,
      didDocumentCount: didDocumentCountValue,
      bondedTokens: bondedTokensAmount,
      tokenBurned: tokenBurnedValue,
      circulatingSupply: circulatingSupply || 0,
    };
  }, [isRpcLoading, allQueries, chainName, circulatingSupply]);

  // Fetch all data
  const fetchAllData = useCallback(async () => {
    if (!chainName || !isDataQueryEnabled) return;

    try {
      // Fetch circulating supply
      await fetchCirculatingSupply();
    } catch (error) {
      console.error("Error updating dashboard data:", error);
    }
  }, [chainName, fetchCirculatingSupply, isDataQueryEnabled]);

  //EFFECTS
  // Effect to reset state and fetch data when chain changes
  useEffect(() => {
    // Clear queries when chain changes
    queriesWithUnchangingKeys.forEach((query) => query.remove());

    // Reset circulating supply when chain changes
    setCirculatingSupply(null);

    // Fetch new data when chain changes
    if (isDataQueryEnabled) {
      fetchAllData();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chainName]);

  // Effect to fetch circulating supply on initial render
  useEffect(() => {
    if (chainName && isDataQueryEnabled) {
      fetchCirculatingSupply();
    }
  }, [chainName, isDataQueryEnabled, fetchCirculatingSupply]);

  return {
    data,
    isLoading,
    fetchAllData,
  };
};
