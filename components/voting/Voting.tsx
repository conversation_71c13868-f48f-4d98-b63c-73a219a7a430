import {
  Proposal as IProposal,
  Proposal<PERSON>tatus,
  TallyResult,
} from "interchain-query/cosmos/gov/v1/gov";
import { VotingList } from "./VotingList";
import { Modal } from "./Modal";
import { chains } from "chain-registry";
import Image from "next/image";
import { useEffect, useState } from "react";
import { useChain } from "@cosmos-kit/react";
import { useModal, useVotingData } from "@/hooks";
import { Proposal } from "@/components";

export function Voting({ chainName }: { chainName: string }) {
  const { address } = useChain(chainName);
  const [proposal, setProposal] = useState<IProposal>();
  const { data, refetch } = useVotingData(chainName);
  console.log("🚀 ~ Voting ~ data:", data);
  const { modal, open: openModal, close: closeModal, setTitle } = useModal("");

  // const chain = chains.find((c) => c.chain_name === chainName);

  // useEffect(() => {
  //   if (!data.proposals || data.proposals.length === 0) return;
  //   data.proposals.forEach((proposal: IProposal) => {
  //     if (proposal.status === ProposalStatus.PROPOSAL_STATUS_VOTING_PERIOD) {
  //       (async () => {
  //         for (const { address } of chain?.apis?.rest || []) {
  //           const api = `${address}/cosmos/gov/v1/proposals/${Number(
  //             proposal.id
  //           )}/tally`;
  //           try {
  //             const tally = (await (await fetch(api)).json()).tally;
  //             if (!tally) {
  //               continue;
  //             }

  //             break;
  //           } catch (e) {}
  //         }
  //       })();
  //     }
  //   });
  // }, [data.proposals?.length, chainName]);

  function onClickProposal(proposal: IProposal) {
    openModal();
    setProposal(proposal);
    // @ts-ignore
    setTitle(`#${proposal.id?.toString()} ${proposal?.title}`);
  }

  return (
    <div className="max-w-5xl mx-auto flex justify-center h-full">
      {address ? (
        <VotingList chainName={chainName} onSelectProposal={onClickProposal} />
      ) : (
        <div className="flex flex-col items-center justify-center gap-4">
          <Image
            src="/assets/navLogo.png"
            alt="empe image"
            width={350}
            height={350}
            className="max-h-[50vh]"
          />
          <p className="text-center text-white text-lg">
            To take full advantage of the EMPE HUB, connect your wallet
          </p>
        </div>
      )}
      {proposal && (
        <Modal
          isOpen={modal.open}
          onClose={closeModal}
          title={proposal.title}
          metadata={proposal.metadata}
          id={proposal.id?.toString()}
          submitTime={proposal.submitTime?.toISOString()}
          votingStartTime={proposal.votingStartTime?.toISOString()}
          votingEndTime={proposal.votingEndTime?.toISOString()}
        >
          <Proposal
            votes={data.votes}
            proposal={proposal}
            chainName={chainName}
            onVoteSuccess={refetch}
            bondedTokens="1000000"
          />
        </Modal>
      )}
    </div>
  );
}
