export const handleGetTestnetCirculatingSupply = async (): Promise<any> => {
  const response = await fetch(
    "https://indexer-testnet.empe.io/circulating_supply",
    {
      method: "GET",
      headers: {
        accept: "application/json",
      },
    }
  );

  return response.json();
};

export const handleGetDevnetCirculatingSupply = async (): Promise<any> => {
  const response = await fetch(
    "https://indexer-devnet.empe.io/circulating_supply",
    {
      method: "GET",
      headers: {
        accept: "application/json",
      },
    }
  );

  return response.json();
};

export const handleGetMainnetCirculatingSupply = async (): Promise<any> => {
  const response = await fetch("https://indexer.empe.io/circulating_supply", {
    method: "GET",
    headers: {
      accept: "application/json",
    },
  });

  return response.json();
};
